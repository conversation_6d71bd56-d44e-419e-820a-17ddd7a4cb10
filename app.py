import streamlit as st
import cv2
import numpy as np
import torch
from ultralytics import YOL<PERSON>
from camera_utils import OptimizedCamera, FrameProcessor
import os

# Konfigurasi halaman Streamlit
st.set_page_config(
    page_title="SIBI Sign Language Detection",
    page_icon="🤟",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Kelas untuk mengelola deteksi SIBI
class SIBIDetector:
    def __init__(self, model_path='sibiv3.pt'):
        self.model_path = model_path
        self.model = None
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.class_names = ["mau", "saya", "mana", "makan", "kamu", "jalan", "hotel", "ke", "di"]
        self.confidence_threshold = 0.5

    def load_model(self):
        """Memuat model YOLOv11 dengan optimasi GPU"""
        try:
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"Model file tidak ditemukan: {self.model_path}")

            # Load model YOLO
            self.model = YOLO(self.model_path)

            # Pindahkan ke GPU jika tersedia
            if self.device == 'cuda':
                self.model.to(self.device)
                st.info(f"Model dimuat di GPU: {torch.cuda.get_device_name()}")
            else:
                st.info("Model dimuat di CPU")

            return True

        except Exception as e:
            st.error(f"Error memuat model: {str(e)}")
            return False

    def detect(self, frame):
        """Melakukan deteksi pada frame"""
        if self.model is None or frame is None:
            return frame, []

        try:
            # Inferensi dengan model
            results = self.model(frame, conf=self.confidence_threshold, verbose=False)

            detections = []
            annotated_frame = frame.copy()

            # Proses hasil deteksi
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Koordinat bounding box
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())

                        # Pastikan class_id valid
                        if 0 <= class_id < len(self.class_names):
                            class_name = self.class_names[class_id]

                            # Simpan deteksi
                            detections.append({
                                'class': class_name,
                                'confidence': float(confidence),
                                'bbox': [int(x1), int(y1), int(x2), int(y2)]
                            })

                            # Gambar bounding box dan label
                            cv2.rectangle(annotated_frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

                            # Label dengan confidence
                            label = f"{class_name}: {confidence:.2f}"
                            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

                            # Background untuk label
                            cv2.rectangle(annotated_frame,
                                        (int(x1), int(y1) - label_size[1] - 10),
                                        (int(x1) + label_size[0], int(y1)),
                                        (0, 255, 0), -1)

                            # Teks label
                            cv2.putText(annotated_frame, label,
                                      (int(x1), int(y1) - 5),
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

            return annotated_frame, detections

        except Exception as e:
            st.error(f"Error dalam deteksi: {str(e)}")
            return frame, []

# Inisialisasi session state
if 'camera' not in st.session_state:
    st.session_state.camera = OptimizedCamera()
if 'frame_processor' not in st.session_state:
    st.session_state.frame_processor = FrameProcessor()
if 'detector' not in st.session_state:
    st.session_state.detector = SIBIDetector()
if 'camera_active' not in st.session_state:
    st.session_state.camera_active = False
if 'model_loaded' not in st.session_state:
    st.session_state.model_loaded = False
if 'detected_words' not in st.session_state:
    st.session_state.detected_words = []
if 'sentence' not in st.session_state:
    st.session_state.sentence = ""

def load_model():
    """Memuat model YOLOv11 dengan GPU support"""
    try:
        with st.spinner("Memuat model SIBI..."):
            success = st.session_state.detector.load_model()
            if success:
                st.session_state.model_loaded = True
                st.success("Model SIBI berhasil dimuat!")
            else:
                st.error("Gagal memuat model SIBI!")

    except Exception as e:
        st.error(f"Error saat memuat model: {str(e)}")

def main():
    st.title("🤟 SIBI Sign Language Detection")
    st.markdown("### Aplikasi Deteksi Bahasa Isyarat Indonesia Real-time")
    
    # Sidebar untuk kontrol
    with st.sidebar:
        st.header("Kontrol Aplikasi")
        
        # Load model button
        if not st.session_state.model_loaded:
            if st.button("📥 Muat Model SIBI", use_container_width=True):
                load_model()
        else:
            st.success("✅ Model SIBI siap digunakan")
        
        st.divider()
        
        # Kontrol kamera
        st.subheader("Kontrol Kamera")
        
        camera_index = st.selectbox("Pilih Kamera:", [0, 1, 2], index=0)
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📹 Mulai Kamera", use_container_width=True):
                if st.session_state.camera.start_capture():
                    st.session_state.camera_active = True
                    st.success("Kamera berhasil dimulai!")
                    st.rerun()
                else:
                    st.error("Gagal memulai kamera!")

        with col2:
            if st.button("⏹️ Stop Kamera", use_container_width=True):
                st.session_state.camera.stop_capture()
                st.session_state.camera_active = False
                st.info("Kamera dihentikan")
                st.rerun()
        
        # Status kamera
        if st.session_state.camera_active:
            st.success("🟢 Kamera Aktif")
        else:
            st.info("🔴 Kamera Tidak Aktif")
        
        st.divider()
        
        # Informasi kata SIBI
        st.subheader("Kata SIBI yang Dapat Dideteksi:")
        sibi_words = ["mau", "saya", "mana", "makan", "kamu", "jalan", "hotel", "ke", "di"]
        for word in sibi_words:
            st.write(f"• {word}")
    
    # Area utama untuk video dan deteksi
    main_col1, main_col2 = st.columns([2, 1])

    with main_col1:
        st.subheader("📹 Live Camera Feed")
        video_placeholder = st.empty()

        # Tampilkan video dengan deteksi jika kamera aktif
        if st.session_state.camera_active and st.session_state.camera.is_active():
            frame = st.session_state.camera.get_latest_frame()
            if frame is not None:
                # Enhance frame untuk deteksi yang lebih baik
                enhanced_frame = st.session_state.frame_processor.enhance_frame(frame)

                # Lakukan deteksi jika model sudah dimuat
                if st.session_state.model_loaded:
                    annotated_frame, detections = st.session_state.detector.detect(enhanced_frame)

                    # Update detected words
                    if detections:
                        for detection in detections:
                            word = detection['class']
                            confidence = detection['confidence']
                            if confidence > 0.7:  # Threshold tinggi untuk akurasi
                                if word not in st.session_state.detected_words:
                                    st.session_state.detected_words.append(word)

                    # Konversi BGR ke RGB untuk Streamlit
                    frame_rgb = cv2.cvtColor(annotated_frame, cv2.COLOR_BGR2RGB)
                else:
                    # Konversi BGR ke RGB untuk Streamlit
                    frame_rgb = cv2.cvtColor(enhanced_frame, cv2.COLOR_BGR2RGB)

                video_placeholder.image(frame_rgb, channels="RGB", use_container_width=True)
            else:
                video_placeholder.info("Menunggu frame dari kamera...")
        else:
            video_placeholder.info("Klik 'Mulai Kamera' untuk memulai deteksi")

    with main_col2:
        st.subheader("📊 Hasil Deteksi")

        if st.session_state.camera_active and st.session_state.model_loaded:
            # Tampilkan kata-kata yang terdeteksi
            st.write("**Kata yang Terdeteksi:**")
            if st.session_state.detected_words:
                for i, word in enumerate(st.session_state.detected_words):
                    st.write(f"{i+1}. {word}")

                # Tombol untuk membuat kalimat
                if st.button("📝 Buat Kalimat", use_container_width=True):
                    st.session_state.sentence = " ".join(st.session_state.detected_words)

                # Tombol untuk reset
                if st.button("🔄 Reset Kata", use_container_width=True):
                    st.session_state.detected_words = []
                    st.session_state.sentence = ""
                    st.rerun()
            else:
                st.info("Belum ada kata yang terdeteksi")

            # Tampilkan kalimat yang dibuat
            if st.session_state.sentence:
                st.write("**Kalimat:**")
                st.success(st.session_state.sentence)
        else:
            st.warning("Pastikan kamera dan model sudah aktif")

if __name__ == "__main__":
    main()
