#!/usr/bin/env python3
"""
Script untuk menjalankan aplikasi SIBI Sign Language Detection
"""

import subprocess
import sys
import os
import torch

def check_requirements():
    """Cek apakah semua requirements sudah terinstall"""
    try:
        import streamlit
        import cv2
        import ultralytics
        import numpy
        import PIL
        print("✅ Semua dependencies sudah terinstall")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Jalankan: pip install -r requirements.txt")
        return False

def check_gpu():
    """Cek status GPU"""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name()
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"✅ GPU tersedia: {gpu_name}")
        print(f"   Memory: {gpu_memory:.1f} GB")
        return True
    else:
        print("⚠️  GPU tidak tersedia, menggunakan CPU")
        return False

def check_model():
    """Cek apakah model file ada"""
    model_path = "sibiv3.pt"
    if os.path.exists(model_path):
        file_size = os.path.getsize(model_path) / 1024**2
        print(f"✅ Model ditemukan: {model_path} ({file_size:.1f} MB)")
        return True
    else:
        print(f"❌ Model tidak ditemukan: {model_path}")
        return False

def check_camera():
    """Cek apakah kamera tersedia"""
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            if ret:
                print("✅ Kamera tersedia dan berfungsi")
                return True
            else:
                print("❌ Kamera tidak dapat mengambil frame")
                return False
        else:
            print("❌ Tidak dapat mengakses kamera")
            return False
    except Exception as e:
        print(f"❌ Error saat cek kamera: {e}")
        return False

def main():
    print("🤟 SIBI Sign Language Detection - Startup Check")
    print("=" * 50)
    
    # Cek semua requirements
    checks = [
        ("Dependencies", check_requirements()),
        ("GPU Support", check_gpu()),
        ("Model File", check_model()),
        ("Camera", check_camera())
    ]
    
    print("\n📋 Hasil Pemeriksaan:")
    print("-" * 30)
    
    all_good = True
    for check_name, result in checks:
        status = "✅ OK" if result else "❌ FAIL"
        print(f"{check_name:15}: {status}")
        if not result and check_name in ["Dependencies", "Model File"]:
            all_good = False
    
    print("-" * 30)
    
    if all_good:
        print("\n🚀 Memulai aplikasi Streamlit...")
        print("   Aplikasi akan terbuka di browser Anda")
        print("   Tekan Ctrl+C untuk menghentikan aplikasi")
        print("\n" + "=" * 50)
        
        # Jalankan Streamlit
        try:
            subprocess.run([
                sys.executable, "-m", "streamlit", "run", "app.py",
                "--server.address", "localhost",
                "--server.port", "8501",
                "--browser.gatherUsageStats", "false"
            ])
        except KeyboardInterrupt:
            print("\n\n👋 Aplikasi dihentikan oleh user")
        except Exception as e:
            print(f"\n❌ Error saat menjalankan aplikasi: {e}")
    else:
        print("\n❌ Tidak dapat memulai aplikasi karena ada masalah")
        print("   Perbaiki masalah di atas terlebih dahulu")

if __name__ == "__main__":
    main()
