# 🤟 SIBI Sign Language Detection

Aplikasi deteksi bahasa isyarat Indonesia (SIBI) real-time menggunakan YOLOv11 dan Streamlit dengan optimasi GPU.

## 📋 Fitur

- **Deteksi Real-time**: Mendeteksi 9 kata SIBI secara real-time melalui kamera
- **GPU Acceleration**: Menggunakan CUDA untuk performa optimal
- **Interface Intuitif**: Antarmuka Streamlit yang mudah digunakan
- **Optimasi Kamera**: Sistem kamera yang dioptimalkan untuk mengurangi lag
- **Pembentukan Kalimat**: Menyusun kata-kata yang terdeteksi menjadi kalimat

## 🎯 Kata SIBI yang Dapat Dideteksi

1. mau
2. saya
3. mana
4. makan
5. kamu
6. jalan
7. hotel
8. ke
9. di

## 🛠️ Instalasi

### Prerequisites

- Python 3.8 atau lebih baru
- CUDA-compatible GPU (opsional, untuk performa optimal)
- Webcam atau kamera eksternal

### Langkah Instalasi

1. **Clone atau download project ini**
   ```bash
   cd sibiweb
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Pastikan model `sibiv3.pt` ada di direktori root**

4. **Jalankan aplikasi**
   ```bash
   python run_app.py
   ```
   
   Atau langsung dengan Streamlit:
   ```bash
   streamlit run app.py
   ```

## 🚀 Cara Penggunaan

1. **Jalankan aplikasi** menggunakan `python run_app.py`
2. **Muat model** dengan klik tombol "📥 Muat Model SIBI"
3. **Mulai kamera** dengan klik tombol "📹 Mulai Kamera"
4. **Lakukan gerakan bahasa isyarat** di depan kamera
5. **Lihat hasil deteksi** di panel sebelah kanan
6. **Buat kalimat** dari kata-kata yang terdeteksi

## ⚙️ Optimasi Performa

### GPU Acceleration
- Aplikasi secara otomatis menggunakan GPU jika tersedia
- Untuk memastikan GPU terdeteksi, pastikan CUDA sudah terinstall

### Pengaturan Kamera
- Resolusi default: 640x480 untuk performa optimal
- Frame rate: 30 FPS
- Buffer minimal untuk real-time processing

### Tips Penggunaan
- Pastikan pencahayaan yang cukup
- Posisikan tangan di tengah frame kamera
- Lakukan gerakan dengan jelas dan tidak terlalu cepat
- Gunakan background yang kontras dengan tangan

## 📁 Struktur Project

```
sibiweb/
├── app.py              # Aplikasi Streamlit utama
├── camera_utils.py     # Utilitas kamera dengan optimasi GPU
├── run_app.py          # Script untuk menjalankan aplikasi
├── requirements.txt    # Dependencies Python
├── sibiv3.pt          # Model YOLOv11 (harus ada)
└── README.md          # Dokumentasi ini
```

## 🔧 Troubleshooting

### Kamera tidak terdeteksi
- Pastikan kamera tidak digunakan aplikasi lain
- Coba ganti index kamera (0, 1, 2) di aplikasi
- Restart aplikasi jika diperlukan

### Model tidak dapat dimuat
- Pastikan file `sibiv3.pt` ada di direktori root
- Cek apakah file model tidak corrupt

### Performa lambat
- Pastikan menggunakan GPU jika tersedia
- Tutup aplikasi lain yang menggunakan banyak resource
- Kurangi resolusi kamera jika diperlukan

### Error CUDA
- Install CUDA toolkit yang sesuai dengan PyTorch
- Pastikan driver GPU sudah update

## 🎯 Pengembangan Selanjutnya

- [ ] Menambah lebih banyak kata SIBI
- [ ] Implementasi grammar checking untuk kalimat
- [ ] Fitur text-to-speech untuk output
- [ ] Mode pelatihan untuk kata baru
- [ ] Export hasil deteksi ke file

## 📞 Support

Jika mengalami masalah atau memiliki pertanyaan, silakan buat issue di repository ini.

## 📄 Lisensi

Project ini dibuat untuk tujuan edukasi dan membantu komunitas tuli dalam berkomunikasi.
