import cv2
import numpy as np
import torch
import time
from threading import Thread, Lock
import queue

class OptimizedCamera:
    """
    Kelas untuk mengelola kamera dengan optimasi GPU dan performa tinggi
    """
    
    def __init__(self, camera_index=0, width=640, height=480, fps=30):
        self.camera_index = camera_index
        self.width = width
        self.height = height
        self.fps = fps
        
        self.cap = None
        self.is_running = False
        self.frame_queue = queue.Queue(maxsize=3)
        self.capture_thread = None
        self.lock = Lock()
        
        # GPU optimization settings
        self.use_gpu = torch.cuda.is_available()
        if self.use_gpu:
            print(f"GPU tersedia: {torch.cuda.get_device_name()}")
        else:
            print("Menggunakan CPU untuk processing")
    
    def initialize_camera(self):
        """Inisialisasi kamera dengan pengaturan optimal"""
        try:
            # Coba berbagai backend untuk performa optimal
            backends = [cv2.CAP_DSHOW, cv2.CAP_V4L2, cv2.CAP_ANY]
            
            for backend in backends:
                try:
                    self.cap = cv2.VideoCapture(self.camera_index, backend)
                    if self.cap.isOpened():
                        break
                except:
                    continue
            
            if not self.cap or not self.cap.isOpened():
                raise Exception("Tidak dapat membuka kamera")
            
            # Optimasi pengaturan kamera
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimal buffer untuk real-time
            
            # Pengaturan tambahan untuk performa
            self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))
            
            # Verifikasi pengaturan
            actual_width = self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)
            actual_height = self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
            actual_fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            print(f"Kamera diinisialisasi: {actual_width}x{actual_height} @ {actual_fps} FPS")
            
            return True
            
        except Exception as e:
            print(f"Error inisialisasi kamera: {e}")
            return False
    
    def start_capture(self):
        """Memulai capture frame dalam thread terpisah"""
        if not self.initialize_camera():
            return False
        
        self.is_running = True
        self.capture_thread = Thread(target=self._capture_loop, daemon=True)
        self.capture_thread.start()
        
        return True
    
    def _capture_loop(self):
        """Loop capture frame yang berjalan di thread terpisah"""
        frame_time = 1.0 / self.fps
        last_time = time.time()
        
        while self.is_running and self.cap and self.cap.isOpened():
            current_time = time.time()
            
            # Kontrol frame rate
            if current_time - last_time >= frame_time:
                ret, frame = self.cap.read()
                
                if ret and frame is not None:
                    # Preprocessing untuk optimasi
                    frame = self._preprocess_frame(frame)
                    
                    # Tambahkan ke queue (hapus frame lama jika penuh)
                    with self.lock:
                        if self.frame_queue.full():
                            try:
                                self.frame_queue.get_nowait()
                            except queue.Empty:
                                pass
                        
                        try:
                            self.frame_queue.put_nowait(frame)
                        except queue.Full:
                            pass
                    
                    last_time = current_time
                else:
                    # Jika gagal baca frame, tunggu sebentar
                    time.sleep(0.01)
            else:
                # Tunggu sampai waktunya frame berikutnya
                time.sleep(0.001)
    
    def _preprocess_frame(self, frame):
        """Preprocessing frame untuk optimasi"""
        # Resize jika diperlukan
        if frame.shape[1] != self.width or frame.shape[0] != self.height:
            frame = cv2.resize(frame, (self.width, self.height))
        
        # Flip horizontal untuk efek mirror
        frame = cv2.flip(frame, 1)
        
        return frame
    
    def get_latest_frame(self):
        """Mendapatkan frame terbaru dari queue"""
        try:
            with self.lock:
                # Ambil frame terbaru, buang yang lama
                latest_frame = None
                while not self.frame_queue.empty():
                    latest_frame = self.frame_queue.get_nowait()
                return latest_frame
        except queue.Empty:
            return None
    
    def stop_capture(self):
        """Menghentikan capture dan membersihkan resources"""
        self.is_running = False
        
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2.0)
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        # Bersihkan queue
        with self.lock:
            while not self.frame_queue.empty():
                try:
                    self.frame_queue.get_nowait()
                except queue.Empty:
                    break
    
    def is_active(self):
        """Cek apakah kamera masih aktif"""
        return self.is_running and self.cap and self.cap.isOpened()
    
    def get_camera_info(self):
        """Mendapatkan informasi kamera"""
        if not self.cap:
            return None
        
        return {
            'width': int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'fps': self.cap.get(cv2.CAP_PROP_FPS),
            'backend': self.cap.getBackendName()
        }

class FrameProcessor:
    """
    Kelas untuk memproses frame dengan optimasi GPU
    """
    
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu and torch.cuda.is_available()
        self.device = 'cuda' if self.use_gpu else 'cpu'
    
    def enhance_frame(self, frame):
        """Meningkatkan kualitas frame untuk deteksi yang lebih baik"""
        if frame is None:
            return None
        
        try:
            # Konversi ke format yang optimal
            if len(frame.shape) == 3 and frame.shape[2] == 3:
                # Normalisasi kontras dan brightness
                frame = cv2.convertScaleAbs(frame, alpha=1.1, beta=10)
                
                # Gaussian blur ringan untuk mengurangi noise
                frame = cv2.GaussianBlur(frame, (3, 3), 0)
            
            return frame
            
        except Exception as e:
            print(f"Error dalam enhance_frame: {e}")
            return frame
    
    def prepare_for_inference(self, frame):
        """Mempersiapkan frame untuk inferensi model"""
        if frame is None:
            return None
        
        try:
            # Konversi BGR ke RGB jika diperlukan
            if len(frame.shape) == 3 and frame.shape[2] == 3:
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            else:
                frame_rgb = frame
            
            return frame_rgb
            
        except Exception as e:
            print(f"Error dalam prepare_for_inference: {e}")
            return frame
